{"validators": {"required": "{{field}} is required", "email": "Please enter a valid email address", "number": "Please enter number", "pattern": "Please enter a valid {{requiredPattern}} value in {{field}}", "minlength": "Please enter at least {{minLength}} characters", "maxlength": "Please enter no more than {{maxLength}} characters", "min": "Please enter a value greater than or equal to {{min}}", "max": "Please enter a value less than or equal to {{max}}", "minDate": "Please enter a value greater than or equal to {{minDate}}", "maxOneDecimal": "{{field}} is positive number with maximum 1 decimal", "maxTwoDecimal": "{{field}} is positive number with maximum 2 decimal", "maxDecimal1": "positive number with maximum 1 decimal", "maxDecimal2": "positive number with maximum 2 decimal", "maxOneDecimalRound5": "{{field}} is positive number with maximum 1 decimal and round up to 0.5", "positiveNumber": "{{field}} is positive number"}, "common": {"mainHeader.app.title": "ONE Record", "mainHeader.app.subtitle": "Living Lab", "mainHeader.mainNav.sli": "SLI", "mainHeader.mainNav.sli.piece": "SLI Piece List", "mainHeader.mainNav.hawb": "HAWB", "mainHeader.mainNav.mawb": "MAWB", "mainHeader.mainNav.upload": "Upload", "mainHeader.mainNav.payment": "Payment", "mainHeader.mainNav.fileBrowser": "File Browser", "mainHeader.mainNav.checkin": "Check-in", "mainHeader.mainNav.mf": "MF", "mainHeader.mainNav.users": "User Management", "mainHeader.settings": "Settings", "mainHeader.account": "Account", "mainHeader.userid": "User ID", "mainHeader.currentRole": "Current Role", "mainHeader.resources": "Resources", "mainHeader.logout": "Logout", "mainHeader.mainNav.partner": "Partner Access", "mainHeader.userlogin.success": "<PERSON><PERSON> successfully", "mainHeader.userlogin.fail": "<PERSON><PERSON> failed", "mainFooter.title": "Our mission is to represent,", "mainFooter.subtitle": "lead and serve the airline industry", "mainFooter.support": "Support", "mainFooter.services": "Services", "mainFooter.store": "IATA store", "mainFooter.privacy": "Privacy", "mainFooter.legal": "Legal", "breadcrumb.home": "Home", "dialog.next": "Next", "dialog.cancel": "Cancel", "dialog.ok": "OK", "dialog.cancel.content": "Are you sure to cancel?", "dialog.delete.content": "Are you sure to delete?", "selectOrg.title": "Select One from Below", "confirm.title": "Confirm", "dialog.form.validate": "Please fill out all the required and valid data.", "dialog.orglist.fail": "Get organization list failed", "dialog.orginfo.fail": "Get organization info failed"}, "sli": {"mgmt.title": "Shipper's Letter of Instructions (SLI) Management", "mgmt.goodsDescription": "Goods Description", "mgmt.sliCode": "SLI Code", "mgmt.shipper": "Shipper Company Name", "mgmt.consignee": "Consignee Company Name", "mgmt.departureLocation": "Origin", "mgmt.arrivalLocation": "Destination", "mgmt.hawbNumber": "Associated HAWB", "mgmt.createDate": "Created Date Range", "mgmt.search": "Search", "mgmt.reset": "Reset", "mgmt.create": "Create SLI", "mgmt.createHawb": "Create", "mgmt.edit": "Edit SLI", "mgmt.cancel": "Cancel", "mgmt.save": "Save", "mgmt.add.piece": "Piece", "mgmt.delete.piece": "Delete", "mgmt.list": "<PERSON><PERSON>'s Letter of Instructions (SLI) List", "mgmt.company": {"companyName": "Company Name", "contactName": "Contact Name", "country": "Country", "province": "Province", "city": "City", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "shipper": "Shipper", "consignee": "Consignee", "alsoNotify": "Also Notify", "companyName.required": "Company Name is required", "contactName.required": "Contact Name is required", "country.required": "Country is required", "province.required": "Province is required", "city.required": "City is required", "address.required": "Address is required", "phoneNumber.required": "Phone Number is required", "pattern": {"number": "Please enter number", "email": "Please enter a valid email address"}}, "mgmt.routing": {"departureLocation": "Airport of Departure", "arrivalLocation": "Airport of Destination", "departureLocation.required": "Airport of Departure is required", "arrivalLocation.required": "Airport of Destination is required", "shippingInfo": "Accounting Information"}, "mgmt.pieceList": {"goodsDescription": "Description of Goods", "totalGrossWeight": "Total Gross Weight", "totalDimensions": "Total Dimensions", "dimLength": "Length", "dimWidth": "<PERSON><PERSON><PERSON>", "dimHeight": "Height", "goodsDescription.required": "Description of Goods is required", "totalGrossWeight.required": "Total Gross Weight is required", "dimLength.required": "Length is required", "dimWidth.required": "Width is required", "dimHeight.required": "Height is required", "declaredValueForCustoms": "Declared Value for Customs", "declaredValueForCarriage": "Declared Value for Carriage", "insuredAmount": "Insurance Amount Requested", "textualHandlingInstructions": "Handling Information Remarks", "weightValuationIndicator": "Weight Valuation Indicator", "incoterms": "Incoterms", "pattern": {"positiveNumber": "positive number", "decimalNumber1": "positive number with maximum 1 decimal", "decimalNumber2": "positive number with maximum 2 decimals", "decimalNumber2NIL": "positive number with maximum 2 decimals or NIL", "decimalNumber2NVD": "positive number with maximum 2 decimals or NVD", "decimalNumber2NCV": "positive number with maximum 2 decimals or NCV"}}, "piece": {"table.column.productDescription": "Product Description", "table.column.packagingType": "Packaging Type", "table.column.grossWeight": "Gross Weight(KG)", "table.column.dimensions": "Dimensions", "table.column.pieceQuantity": "Piece Quantity", "table.column.slac": "SLAC", "table.column.actions": "Actions", "addDialog.title": "Choose Piece Type", "addDialog.subtitle": "Please select piece type of you want to add", "addDialog.general": "General <PERSON>", "addDialog.dg": "Dangerous Goods", "addDialog.la": "Live Animals", "addDialog.pieceType.required": "Piece type is required", "grossWeight.required": "Gross Weight is required", "packagingType.required": "Packaging Type is required", "productDescription.required": "Product Description is required", "add": "Add Piece", "edit": "Edit Piece", "add.pieceIn": "Add", "title": "Piece", "productDescription": "Product Description", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "upid": "UPID", "packages": "Packages", "packagingType": "Type of Package", "packagedIdentifier": "Packaged Identifier", "hsCommodityDescription": "HS Commodity Description", "nvdForCustoms": "Whether have Declared Value for Customs", "nvdForCarriage": "Whether have Declared Value for Carriage", "shippingMarks": "Shipping Marks", "textualHandlingInstructions": "Handling Instructions", "pieceQuantity": "Piece Quantity", "pieceQuantity.required": "Piece Quantity is required", "done": "Done", "item.title": "Do you want to create Contained Pieces in this Piece?", "item.add": "Add Item", "contained.yes": "Yes", "contained.no": "No", "contained.title": "Contained Pieces", "item.description": "Item Description", "item.weight": "Weight", "item.quantity": "Quantity", "item.total": "Total Items", "slac.total": "SLAC", "create.success": "Create piece successfully", "create.fail": "Create piece failed", "update.success": "Update piece successfully", "update.fail": "Update piece failed", "detail.fail": "Get piece detail failed", "list.fail": "Get piece list failed", "no.sli.fail": "Please create SLI first"}, "dgPiece": {"title": "DG Piece", "formItem": {"productDescription": "Product Description", "typeOfPackage": "Type of Package", "packagedIdentifier": "Packaged Identifier", "whetherHaveDeclaredValueForCustoms": "Whether have Declared Value for Customs", "whetherHaveDeclaredValueForCarriage": "Whether have Declared Value for Carriage", "specialProvisionId": "Special Provision ID", "explosiveCompatibilityGroupCode": "Explosive Compatibility Group Code", "packagingDangerLevelCode": "Packaging Danger Level Code", "technicalName": "Technical Name", "unNumber": "UN Number", "shippersDeclaration": "<PERSON><PERSON>’s Declaration", "handlingInformation": "Handling Information", "allPackedInOne": "All Packed in One", "qValueNumeric": "Q Value Numeric", "upid": "UPID", "shippingMarks": "Shipping Marks", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "hsCommodityDescription": "HS Commodity Description", "properShippingName": "Proper Shipping Name", "textualHandlingInstructions": "Textual Handling Instructions", "hazardClassificationId": "Hazard Classification ID", "additionalHazardClassificationId": "Additional Hazard Classification ID", "packingInstructionNumber": "Packing Instruction Number", "complianceDeclaration": "Compliance Declaration", "exclusiveUseIndicator": "Exclusive Use Indicator", "authorizationInformation": "Authorization Information", "aircraftLimitationInformation": "Aircraft Limitation Information", "item.productDescription": "Product Description", "item.quantity": "Quantity", "item.weight": "Weight", "item.title": "<PERSON><PERSON>", "item.add": "Add"}}, "liveAnimalPiece": {"title": "Live Animal Piece", "formItem": {"productDescription": "Product Description", "typeOfPackage": "Type of Package", "packagedIdentifier": "Packaged Identifier", "speciesCommonName": "Species Common Name", "speciesScientificName": "Species Scientific Name", "specimenDescription": "Specimen Description", "animalQuantity": "Animal Quantity", "shippingMarks": "Shipping Marks", "upid": "UPID", "grossWeight": "Gross Weight", "dimensions": "Dimensions", "whetherHaveDeclaredValueForCustoms": "Whether have Declared Value for Customs", "whetherHaveDeclaredValueForCarriage": "Whether have Declared Value for Carriage", "textualHandlingInstructions": "Textual Handling Instructions"}}, "table.column.sliCode": "SLI Code", "table.column.shipper": "Shipper", "table.column.consignee": "Consignee", "table.column.goodsDescription": "Goods Description", "table.column.departureLocation": "Origin", "table.column.arrivalLocation": "Destination", "table.column.slac": "Piece Quantity", "table.column.createDate": "Created Date", "table.column.receivedFrom": "Received From", "table.column.hawbNumber": "Associated HAWB", "table.column.share": "Share", "dialog.create.success": "Create SLI successfully", "dialog.create.fail": "Create SLI failed", "dialog.update.success": "Update SLI successfully", "dialog.update.fail": "Update SLI failed", "dialog.detail.fail": "Get SLI detail failed", "dialog.list.fail": "Get SLI list failed", "dialog.shipper.fail": "Get shipper info failed", "share.title": "Share SLI"}, "shared": {"table.column.name": "Company Name", "table.column.orgType": "Company Type", "button.name": "Share"}, "hawb": {"mgmt.title": "House Air Waybill", "mgmt.hawbNumber": "HAWB Number", "mgmt.create": "Create HAWB from SLI", "mgmt.create.fromSli": "Create House Air Waybill from SLI", "mgmt.create.fromSliDetail": "Create House Air Waybill", "table.column.hawbNumber": "HAWB Number", "table.column.shipper": "Shipper", "table.column.consignee": "Consignee", "table.column.goodsDescription": "Goods Description", "table.column.origin": "Origin", "table.column.destination": "Destination", "table.column.pieceQuantity": "Piece Quantity", "table.column.createDate": "Created Date", "table.column.mawbNumber": "Associated MAWB", "table.column.share": "Share", "dialog.list.fail": "Get HAWB list failed", "createHawb.success": "Create HAWB Success", "preview.awb": "Preview AWB", "updateHawb.success": "HAWB is successfully updated", "updateHawb.error": "HAWB update fails due to server unavailability, please try again", "share.title": "Share HAWB", "carrierAgent": {"title": "Carrier's Agent", "company": "Company", "agentIataCode": "Agent's IATA Code", "country": "Country", "province": "Province", "cityName": "City Name", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "email": "Email Address", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "Issued By", "content": "To be populated when MAWB is created"}, "formItem": {"hawbPrefix": "HAWB Prefix", "hawbNumber": "HAWB Number", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "rateClass": "Rate Class", "chargeableWeight": "Chargeable Weight", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "Airport of Departure (Addr, of First Carrier) and Requested Routing", "departureAndRequestedRouting.required": "Airport of Departure and Requested Routing is required", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "flight": "Flight", "to": "To", "toBy2ndCarrier": "To (by 2nd_carrier)", "toBy3rdCarrier": "To (by 3rd carrier)", "date": "Date", "byFirstCarrier": "By First Carrier", "by2ndCarrier": "By 2nd carrier", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}}, "mawb": {"mgmt.title": "Master Air Waybill", "mgmt.mawbNumber": "MAWB Number", "mgmt.create": "Create MAWB from HAWBs", "mgmt.create.fromHawb": "Create MAWB from my HAWBs", "mgmt.create.fromHawbDetail": "Master Air Waybill", "mgmt.create.fromSelectedHawb": "Select to create MAWB", "mgmt.airlineCode": "Airline Code", "mgmt.latestStatus": "Latest Status", "mgmt.accountingNoteText.shipper": "<PERSON><PERSON>'s Account Number", "mgmt.accountingNoteText.consignee": "Consignee's Account Number", "mgmt.shipment.shipper": "New Shipper", "mgmt.shipment.consignee": "New Consignee", "table.column.share": "Share", "table.column.mawbNumber": "MAWB Number", "table.column.airlineCode": "Airline Code", "table.column.goodsDescription": "Goods Description", "table.column.origin": "Origin", "table.column.destination": "Destination", "table.column.latestStatus": "Latest Status", "table.column.createDate": "Created Date", "share.title": "Share MAWB", "createMawb.success": "Create MAWB Success", "preview.awb": "Preview AWB", "updateMawb.success": "MAWB is successfully updated", "updateMawb.error": "MAWB update fails due to server unavailability, please try again", "carrierAgent": {"title": "Carrier's Agent", "company": "Company", "agentIataCode": "Agent's IATA Code", "accountingNoteText": "Account No.", "country": "Country", "province": "Province", "cityName": "City Name", "textualPostCode": "Postal Code", "address": "Address", "phoneNumber": "Phone Number", "email": "Email Address", "formInvalid": "Carrier's Agent Form Invalid"}, "issuedBy": {"title": "Issued By", "content": "To be populated when MAWB is created"}, "formItem": {"mawbPrefix": "MAWB Prefix", "mawbNumber": "MAWB Number", "mawbNumber.checkLength": "MAWB Number must be 8 digits", "mawbNumber.checkDigit": "MAWB Number's 8th digit should be the mod7 of the first 7 digits, which is ", "accountingInformation": "Accounting Information", "handingInformation": "Handing Information", "noOfPiecesRcp": "No. of Pieces RCP", "grossWeight": "Gross Weight", "serviceCode": "Service Code", "rateClass": "Rate Class", "chargeableWeight": "Chargeable Weight", "rateCharge": "Rate/Charge", "total": "Total", "natureAndQuantityOfGoods": "Nature and Quantity of Goods (including Dimensions or Volume)", "destinationCurrencyRate": "Currency Conversion Rate", "destinationCollectCharges": "Collect Charges in Destination Currency", "totalCollectCharges": "Total Collect Charges", "destinationCharges": "Charges at Destination", "shippingInfo": "Optional Shipping Information", "shippingRefNo": "Reference Number", "date": "Date", "atPlace": "At(Place)", "signatureOfShipperOrHisAgent": "Signature of <PERSON><PERSON> or his Agent", "signatureOfCarrierOrItsAgent": "Signature of Carrier or its Agent"}, "airportInfo": {"departureAndRequestedRouting": "Airport of Departure and Requested Routing", "airportOfDestination": "Airport Of Destination", "amountOfInsurance": "Amount Of Insurance", "chargesCode": "Charges Code", "flight": "Flight", "to": "To", "toBy2ndCarrier": "To (by 2nd_carrier)", "toBy3rdCarrier": "To (by 3rd carrier)", "date": "Date", "byFirstCarrier": "By First Carrier", "by2ndCarrier": "By 2nd carrier", "by3rdCarrier": "By 3rd carrier", "wtOrVal": "WT/VAL", "other": "Other", "declaredValueForCarriage": "Declared Value For Carriage", "declaredValueForCustoms": "Declared Value For Customs"}, "prepaidAndCollect": {"prepaidTitle": "Prepaid", "collectTitle": "Collect", "weightCharge": "Weight Charge", "valuationCharge": "Valuation Charge", "tax": "Tax", "totalOtherChargesDueAgent": "Total Other Charges Due Agent", "totalOtherChargesDueCarrier": "Total Other Charges Due Carrier", "totalPrepaid": "Total Prepaid", "totalCollect": "Total Collect"}, "otherCharges": {"title": "Other Charges", "chargePaymentType": "Charge Payment Type", "entitlement": "Entitlement", "otherChargeCode": "Other Charge Code", "otherChargeAmount": "Other Charge Amount", "addButton": "Other Charges", "formInvalid": "Carrier's Agent Form Invalid"}}, "partner": {"mgmt.head": "Partner Access", "mgmt.title": "Please note following rules have been pre-configured as such:", "mgmt.title1": "1. Airlines can only access MAWBs if MAWB Prefix = Airline Settlement Code with full permissions granted", "mgmt.title2": "2. Shippers can only access HAWBs created from his company's SLIs with full permissions granted.", "mgmt.mawbNumber": "MAWB Number", "mgmt.addPartner": "Add Partner", "mgmt.edit": "Edit", "mgmt.save": "Save", "mgmt.latestStatus": "Latest Status", "table.column.businessData": "Business Data", "table.column.partner": "Partner", "table.column.GET_LOGISTICS_OBJECT": "GET_LOGISTICS_OBJECT", "table.column.PATCH_LOGISTICS_OBJECT": "PATCH_LOGISTICS_OBJECT", "table.column.POST_LOGISTICS_EVENT": "POST_LOGISTICS_EVENT", "table.column.GET_LOGISTICS_EVENT": "GET_LOGISTICS_EVENT"}, "upload": {"browser.dragAndDrop": "Drag & Drop file(s) here", "browser.or": "or", "browser.browse": "Browse for file(s)", "progress.lastModified": "Last modified at:", "progress.fileSize": "File size:"}, "filesManagement": {"category": "File category", "table.column.name": "Name", "table.column.size": "Size", "table.column.createdAt": "Created at", "table.column.actions": "Actions", "table.action.view": "View", "table.action.download": "Download", "table.action.delete": "Delete", "noFilesFound": "Sorry, we didn't find any files. You might need to upload them first!", "selectedFile.header": "Selected file view"}, "ifgPayment": {"paymentInitFailed": "Payment initiation failed!", "renderFailed": "Payment screen rendering failed!"}, "checkin": {"passenger": {"title": "Passenger details", "name": "Full Name", "gender": "Gender", "nationality": "Nationality", "nationality.placeholder": "Select Nationality", "birthDate": "Birth Date"}, "flight": {"title": "Flight details", "carrier": "Operating Carrier", "carrier.placeholder": "Select Carrier", "departureAirport": "Departure Airport", "departureAirport.placeholder": "Select Airport", "departureDate": "Departure Date", "arrivalAirport": "Arrival Airport", "arrivalAirport.placeholder": "Select Airport", "arrivalDate": "Arrival Date", "flightNumber": "Flight Number", "addLeg": "Add Leg", "deleteLeg": "Delete Leg", "checkin": "Check-in"}, "boardingPass": {"title": "Here is your boarding pass!", "subTitle": "Send boarding pass to your e-mail address:", "email": "Email", "send": "Send"}}, "users": {"mgmt.list": "User Management", "mgmt.keyword": "Please input keyword to search", "mgmt.search": "Search", "table.column.userName": "User Name", "table.column.email": "User Email", "table.column.firstName": "First Name", "table.column.lastName": "Last Name", "table.column.orgName": "Company/Organization", "table.column.primaryOrgName": "Primary Resident Company", "table.column.userType": "User Type", "table.column.roles": "Roles", "table.column.status": "Status", "table.column.lastAccessed": "Last Accessed At", "table.column.actions": "Actions", "table.action.updateUser": "Update User", "buttons.create": "Add User", "noDataFound": "We didn't find any existing user!", "mgmt.create.title": "Create User", "mgmt.create.firstName": "First Name", "mgmt.create.firstName.required": "First Name is required", "mgmt.create.lastName": "Last Name", "mgmt.create.lastName.required": "Last Name is required", "mgmt.create.email": "Email", "mgmt.create.email.required": "Email is required", "mgmt.create.orgName": "Company/Organization", "mgmt.create.orgName.required": "Company/Organization is required", "mgmt.create.primaryOrgName": "Primary Resident Company", "mgmt.create.userType": "User Type", "mgmt.create.userType.required": "User Type are required", "mgmt.create.primaryOrgName.required": "Primary Resident Company is required", "mgmt.create.secondary": "Secondary Resident Companies", "mgmt.create.secondary.add": "Add", "mgmt.create.email.iataEmail": "Email should be @iata.org or @external.iata.org", "create.email.duplicatedUser": "User with such email already exists", "create.roles": "Roles", "create.roles.required": "Roles are required", "create.status": "Status", "create.status.required": "Status is required", "create.cancel": "Cancel", "create.submit": "Save", "update.title": "Update User", "update.submit": "Save"}, "pagination": {"itemsPerPage": "Records per page", "nextPage": "Next page", "previousPage": "Previous page", "firstPage": "First page", "lastPage": "Last page", "rangeEmpty": "0 of 0", "rangeLabel": "{{start}} - {{end}} of {{length}}"}}